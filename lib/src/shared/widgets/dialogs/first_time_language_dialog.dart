import 'dart:ui';

import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/services/app_settings/controller/settings_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class FirstTimeLanguageDialog extends HookConsumerWidget {
  final VoidCallback? onLanguageSelected;

  const FirstTimeLanguageDialog({super.key, this.onLanguageSelected});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsController = ref.read(settingsControllerProvider);

    // Detect device language and set as default
    final deviceLocale = PlatformDispatcher.instance.locale;
    final isDeviceArabic = deviceLocale.languageCode == 'ar';
    final selectedLanguage = useState<String>(isDeviceArabic ? 'ar' : 'en');

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // App Icon/Logo
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: ColorManager.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.language,
                size: 40,
                color: ColorManager.primaryColor,
              ),
            ),

            const SizedBox(height: 24),

            // Welcome Title
            Text(
              context.tr.welcomeToConnectify,
              style: context.title.copyWith(
                fontWeight: FontWeight.bold,
                fontSize: 24,
                color: ColorManager.primaryColor,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 12),

            // Subtitle
            Text(
              context.tr.chooseYourPreferredLanguage,
              style: context.subTitle.copyWith(
                color: Colors.grey[600],
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            // Language Options
            Column(
              children: [
                // English Option
                GestureDetector(
                  onTap: () => selectedLanguage.value = 'en',
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    margin: const EdgeInsets.only(bottom: 12),
                    decoration: BoxDecoration(
                      color: selectedLanguage.value == 'en'
                          ? ColorManager.primaryColor.withOpacity(0.1)
                          : Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: selectedLanguage.value == 'en'
                            ? ColorManager.primaryColor
                            : Colors.grey[300]!,
                        width: 2,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: selectedLanguage.value == 'en'
                                ? ColorManager.primaryColor
                                : Colors.grey[400],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Center(
                            child: Text(
                              'EN',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Text(
                            context.tr.english,
                            style: context.title.copyWith(
                              fontWeight: selectedLanguage.value == 'en'
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                              color: selectedLanguage.value == 'en'
                                  ? ColorManager.primaryColor
                                  : Colors.grey[700],
                            ),
                          ),
                        ),
                        if (selectedLanguage.value == 'en')
                          const Icon(
                            Icons.check_circle,
                            color: ColorManager.primaryColor,
                            size: 24,
                          ),
                      ],
                    ),
                  ),
                ),

                // Arabic Option
                GestureDetector(
                  onTap: () => selectedLanguage.value = 'ar',
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: selectedLanguage.value == 'ar'
                          ? ColorManager.primaryColor.withOpacity(0.1)
                          : Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: selectedLanguage.value == 'ar'
                            ? ColorManager.primaryColor
                            : Colors.grey[300]!,
                        width: 2,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: selectedLanguage.value == 'ar'
                                ? ColorManager.primaryColor
                                : Colors.grey[400],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Center(
                            child: Text(
                              'ع',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Text(
                            context.tr.arabic,
                            style: context.title.copyWith(
                              fontWeight: selectedLanguage.value == 'ar'
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                              color: selectedLanguage.value == 'ar'
                                  ? ColorManager.primaryColor
                                  : Colors.grey[700],
                            ),
                          ),
                        ),
                        if (selectedLanguage.value == 'ar')
                          const Icon(
                            Icons.check_circle,
                            color: ColorManager.primaryColor,
                            size: 24,
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 32),

            // Continue Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  // Update language
                  await settingsController.updateLanguage(
                    Locale(selectedLanguage.value),
                  );

                  // Mark as seen
                  await GetStorageService.setLocalData(
                    key: LocalKeys.haveSeenLanguageSelection,
                    value: true,
                  );

                  // Call callback to notify parent
                  onLanguageSelected?.call();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorManager.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  context.tr.continueWithLanguage,
                  style: context.title.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
